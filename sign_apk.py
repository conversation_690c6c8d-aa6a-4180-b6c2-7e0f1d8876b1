#!/usr/bin/env python3
"""
APK签名工具
用于签名生成的APK文件，使其可以安装到Android设备上

使用方法:
python3 sign_apk.py <apk_file>
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def check_java_tools():
    """检查Java工具是否可用"""
    tools = ['keytool', 'jarsigner']
    missing = []
    
    for tool in tools:
        if not subprocess.run(['which', tool], capture_output=True).returncode == 0:
            missing.append(tool)
    
    if missing:
        print(f"错误: 缺少必要工具: {', '.join(missing)}")
        print("请安装Java Development Kit (JDK)")
        print("Ubuntu/Debian: sudo apt install openjdk-8-jdk")
        print("macOS: brew install openjdk@8")
        return False
    
    return True

def create_debug_keystore():
    """创建调试用的密钥库"""
    keystore_path = 'debug.keystore'
    
    if os.path.exists(keystore_path):
        print(f"使用现有密钥库: {keystore_path}")
        return keystore_path
    
    print("创建调试密钥库...")
    
    # 创建密钥库的命令
    cmd = [
        'keytool', '-genkey', '-v',
        '-keystore', keystore_path,
        '-alias', 'debugkey',
        '-keyalg', 'RSA',
        '-keysize', '2048',
        '-validity', '10000',
        '-storepass', 'android',
        '-keypass', 'android',
        '-dname', 'CN=Debug,OU=Debug,O=Debug,L=Debug,S=Debug,C=US'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"密钥库创建成功: {keystore_path}")
            return keystore_path
        else:
            print(f"创建密钥库失败: {result.stderr}")
            return None
    except Exception as e:
        print(f"创建密钥库时出错: {e}")
        return None

def sign_apk(apk_file, keystore_path):
    """签名APK文件"""
    if not os.path.exists(apk_file):
        print(f"错误: APK文件不存在: {apk_file}")
        return False
    
    if not os.path.exists(keystore_path):
        print(f"错误: 密钥库不存在: {keystore_path}")
        return False
    
    print(f"正在签名APK: {apk_file}")
    
    # 签名命令
    cmd = [
        'jarsigner', '-verbose',
        '-sigalg', 'SHA1withRSA',
        '-digestalg', 'SHA1',
        '-keystore', keystore_path,
        '-storepass', 'android',
        '-keypass', 'android',
        apk_file, 'debugkey'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("APK签名成功!")
            return True
        else:
            print(f"签名失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"签名过程出错: {e}")
        return False

def verify_apk_signature(apk_file):
    """验证APK签名"""
    print(f"验证APK签名: {apk_file}")
    
    cmd = ['jarsigner', '-verify', '-verbose', apk_file]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ APK签名验证成功")
            return True
        else:
            print(f"✗ 签名验证失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"验证签名时出错: {e}")
        return False

def get_apk_info(apk_file):
    """获取APK信息"""
    print(f"\nAPK文件信息:")
    print(f"文件: {apk_file}")
    print(f"大小: {os.path.getsize(apk_file):,} 字节")
    print(f"路径: {os.path.abspath(apk_file)}")

def main():
    """主函数"""
    print("=== APK签名工具 ===")
    
    if len(sys.argv) != 2:
        print("使用方法: python3 sign_apk.py <apk_file>")
        
        # 自动查找APK文件
        apk_files = [f for f in os.listdir('.') if f.endswith('.apk')]
        if apk_files:
            print(f"\n发现APK文件:")
            for i, apk in enumerate(apk_files, 1):
                print(f"{i}. {apk}")
            
            try:
                choice = input(f"\n请选择要签名的APK (1-{len(apk_files)}): ")
                index = int(choice) - 1
                if 0 <= index < len(apk_files):
                    apk_file = apk_files[index]
                else:
                    print("无效选择")
                    return False
            except (ValueError, KeyboardInterrupt):
                print("操作取消")
                return False
        else:
            print("当前目录下没有找到APK文件")
            return False
    else:
        apk_file = sys.argv[1]
    
    # 检查工具
    if not check_java_tools():
        return False
    
    # 检查APK文件
    if not os.path.exists(apk_file):
        print(f"错误: APK文件不存在: {apk_file}")
        return False
    
    print(f"\n目标APK: {apk_file}")
    
    # 创建或使用现有密钥库
    keystore_path = create_debug_keystore()
    if not keystore_path:
        return False
    
    # 签名APK
    if not sign_apk(apk_file, keystore_path):
        return False
    
    # 验证签名
    if not verify_apk_signature(apk_file):
        return False
    
    # 显示APK信息
    get_apk_info(apk_file)
    
    print(f"\n✓ APK签名完成!")
    print(f"现在可以安装APK到Android设备:")
    print(f"adb install {apk_file}")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序异常: {e}")
        sys.exit(1)
