# Supershell ELF to APK Converter

这个工具可以将supershell生成的ELF格式的Android payload转换为可安装的APK文件。

## 功能特点

- 自动检测ELF文件格式
- 生成完整的Android APK结构
- 包含自启动和持久化功能
- 支持多种架构（arm64, armv7, x86, x86_64）
- 隐蔽运行，伪装成系统更新服务

## 使用方法

### 基础用法

```bash
# 转换当前目录下名为 "1" 的ELF文件
python3 payload_to_apk.py

# 指定ELF文件路径
python3 payload_to_apk.py /path/to/your/elf_file
```

### 高级用法

```bash
# 使用完整功能的转换器
python3 elf_to_apk.py your_elf_file output.apk

# 使用简化版本
python3 simple_elf_to_apk.py
```

## 文件说明

1. **payload_to_apk.py** - 推荐使用的主要转换工具
   - 功能最完整
   - 自动检测ELF文件
   - 生成可用的APK结构

2. **elf_to_apk.py** - 高级转换工具
   - 需要Android SDK工具
   - 生成更标准的APK
   - 支持自定义配置

3. **simple_elf_to_apk.py** - 简化版本
   - 不需要额外依赖
   - 基础APK结构
   - 适合快速转换

## 系统要求

### 基础要求
- Python 3.6+
- 目标ELF文件

### 高级功能要求（可选）
- Android SDK Build Tools
- Java Development Kit (JDK)
- keytool（用于签名）
- jarsigner（用于签名）

## 安装依赖

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install python3 python3-pip openjdk-8-jdk
```

### 安装Android SDK（可选）
```bash
# 下载Android SDK
wget https://dl.google.com/android/repository/commandlinetools-linux-latest.zip
unzip commandlinetools-linux-latest.zip
export ANDROID_HOME=$HOME/android-sdk
export PATH=$PATH:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools
```

## 使用步骤

1. **准备ELF文件**
   ```bash
   # 确保你有supershell生成的ELF文件
   file your_elf_file  # 应该显示 ELF 格式
   ```

2. **运行转换器**
   ```bash
   python3 payload_to_apk.py your_elf_file
   ```

3. **签名APK（如果需要）**
   ```bash
   # 生成密钥库
   keytool -genkey -v -keystore my.keystore -alias mykey -keyalg RSA -keysize 2048 -validity 10000
   
   # 签名APK
   jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my.keystore your_payload.apk mykey
   ```

4. **安装APK**
   ```bash
   # 通过ADB安装
   adb install your_payload.apk
   
   # 或者直接传输到Android设备安装
   ```

## APK结构说明

生成的APK包含以下组件：

### 应用组件
- **MainActivity** - 主活动，启动后立即隐藏
- **SystemService** - 后台服务，执行payload
- **AutoStartReceiver** - 广播接收器，实现自启动

### Payload位置
- `assets/data` - 作为资产文件
- `lib/*/libcore.so` - 作为native库
- `res/raw/data.bin` - 作为资源文件

### 权限
- 网络访问权限
- 存储读写权限
- 开机自启权限
- 系统级权限

## 注意事项

### 安全警告
⚠️ **此工具仅用于合法的安全测试和教育目的**
- 请确保你有权限在目标设备上进行测试
- 不要用于恶意目的
- 遵守当地法律法规

### 技术限制
- 生成的APK需要签名才能安装
- 某些Android版本可能有额外的安全限制
- 需要用户手动允许"未知来源"安装

### 兼容性
- 支持Android 4.0+
- 兼容arm64、armv7、x86、x86_64架构
- 在不同Android版本上的行为可能有差异

## 故障排除

### 常见问题

1. **"未找到ELF文件"**
   - 确保文件存在且路径正确
   - 检查文件是否为有效的ELF格式

2. **"APK创建失败"**
   - 检查磁盘空间是否足够
   - 确保有写入权限

3. **"签名失败"**
   - 安装JDK和相关工具
   - 检查密钥库路径和密码

4. **"安装失败"**
   - 启用"未知来源"安装
   - 确保APK已正确签名
   - 检查设备架构兼容性

### 调试模式

```bash
# 查看APK内容
unzip -l your_payload.apk

# 验证APK结构
aapt dump badging your_payload.apk

# 检查签名
jarsigner -verify -verbose your_payload.apk
```

## 免责声明

本工具仅供安全研究和教育目的使用。使用者需要：

1. 确保在合法授权的环境中使用
2. 遵守所有适用的法律法规
3. 不得用于任何恶意或非法目的
4. 承担使用本工具的所有责任和风险

作者不对因使用本工具而造成的任何损害或法律后果承担责任。

## 许可证

本项目仅供教育和研究目的使用。请负责任地使用。
